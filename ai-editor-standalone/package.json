{"name": "ai-editor-standalone", "version": "1.0.0", "description": "独立的AI编辑器组件，基于Novel + TipTap + ProseMirror技术栈", "main": "dist/index.js", "module": "dist/index.esm.js", "types": "dist/index.d.ts", "files": ["dist", "README.md"], "scripts": {"dev": "vite", "build": "tsc && vite build", "build:lib": "vite build --mode lib", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "format": "prettier --write \"src/**/*.{ts,tsx,css,md}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,css,md}\""}, "keywords": ["ai-editor", "rich-text-editor", "novel", "tiptap", "prose<PERSON><PERSON>r", "react", "typescript"], "author": "Your Name", "license": "MIT", "dependencies": {"novel": "^1.0.2", "@tiptap/react": "^2.11.7", "@tiptap/extension-document": "^2.12.0", "@tiptap/extension-text": "^2.12.0", "tiptap-markdown": "^0.8.10", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-separator": "^1.1.4", "@radix-ui/react-tooltip": "^1.2.0", "lucide-react": "^0.487.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "tailwind-merge": "^3.2.0", "use-debounce": "^10.0.4", "react": "^18.0.0", "react-dom": "^18.0.0"}, "devDependencies": {"@types/react": "^18.2.0", "@types/react-dom": "^18.2.0", "@types/node": "^20.0.0", "@typescript-eslint/eslint-plugin": "^6.0.0", "@typescript-eslint/parser": "^6.0.0", "@vitejs/plugin-react": "^4.0.0", "autoprefixer": "^10.4.0", "eslint": "^8.45.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.0", "postcss": "^8.4.0", "prettier": "^3.0.0", "tailwindcss": "^3.3.0", "@tailwindcss/typography": "^0.5.16", "typescript": "^5.0.0", "vite": "^4.4.0", "vite-plugin-dts": "^3.0.0"}, "peerDependencies": {"react": ">=16.8.0", "react-dom": ">=16.8.0"}, "repository": {"type": "git", "url": "https://github.com/your-username/ai-editor-standalone.git"}, "bugs": {"url": "https://github.com/your-username/ai-editor-standalone/issues"}, "homepage": "https://github.com/your-username/ai-editor-standalone#readme"}