import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import dts from 'vite-plugin-dts'
import { resolve } from 'path'

// https://vitejs.dev/config/
export default defineConfig(({ mode }) => {
  const isLib = mode === 'lib'
  
  return {
    plugins: [
      react(),
      ...(isLib ? [dts({ include: ['src'] })] : [])
    ],
    resolve: {
      alias: {
        '@': resolve(__dirname, './src'),
      },
    },
    ...(isLib ? {
      build: {
        lib: {
          entry: resolve(__dirname, 'src/index.ts'),
          name: 'AIEditor',
          formats: ['es', 'umd'],
          fileName: (format) => `index.${format === 'es' ? 'esm' : format}.js`
        },
        rollupOptions: {
          external: [
            'react',
            'react-dom',
            'react/jsx-runtime'
          ],
          output: {
            globals: {
              react: 'React',
              'react-dom': 'ReactDOM',
              'react/jsx-runtime': 'react/jsx-runtime'
            }
          }
        }
      }
    } : {
      build: {
        outDir: 'dist-demo'
      }
    }),
    css: {
      postcss: './postcss.config.js'
    }
  }
})
