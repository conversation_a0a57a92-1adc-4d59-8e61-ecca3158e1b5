/**
 * @fileoverview Default CLIEngineOptions.
 * <AUTHOR>
 */

"use strict";

module.exports = {
    configFile: null,
    baseConfig: false,
    rulePaths: [],
    useEslintrc: true,
    envs: [],
    globals: [],
    extensions: null,
    ignore: true,
    ignorePath: void 0,
    cache: false,

    /*
     * in order to honor the cacheFile option if specified
     * this option should not have a default value otherwise
     * it will always be used
     */
    cacheLocation: "",
    cacheFile: ".eslintcache",
    cacheStrategy: "metadata",
    fix: false,
    allowInlineConfig: true,
    reportUnusedDisableDirectives: void 0,
    globInputPaths: true
};
